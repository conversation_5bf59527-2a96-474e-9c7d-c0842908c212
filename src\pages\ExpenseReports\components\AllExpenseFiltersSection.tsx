import { useState } from 'react';
import { FiSearch } from 'react-icons/fi';
import type { DateRange } from 'react-day-picker';
import Dropdown from '../../../components/Dropdown';
import FormInput from '../../../components/FormInput';
import { Button } from '../../../components/Button';
import DateRangePicker from '../../../components/DateRangePicker';

interface FiltersSectionProps {
  onFiltersChange?: (filters: any) => void;
}

const AllExpenseFiltersSection = ({ onFiltersChange }: FiltersSectionProps) => {
  const [filters, setFilters] = useState({
    submissionDateRange: undefined as DateRange | undefined,
    department: '',
    approver: '',
    approvalStatus: '',
    amountRange: '',
    paymentMethod: '',
    employee: '',
    policyViolations: false,
  });

  const departmentOptions = ['HR', 'Finance', 'IT', 'Marketing', 'Sales'];
  const approvalStatusOptions = ['Pending', 'Approved', 'Rejected', 'In Review'];
  const amountRangeOptions = ['$0 - $100', '$100 - $500', '$500 - $1000', '$1000+'];
  const paymentMethodOptions = ['Credit Card', 'Cash', 'Bank Transfer', 'Check'];

  const handleFilterChange = (key: string, value: string | boolean | DateRange | undefined) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const handleReset = () => {
    const resetFilters = {
      submissionDateRange: undefined as DateRange | undefined,
      department: '',
      approver: '',
      approvalStatus: '',
      amountRange: '',
      paymentMethod: '',
      employee: '',
      policyViolations: false,
    };
    setFilters(resetFilters);
    onFiltersChange?.(resetFilters);

    alert('Filters reset');
  };

  const handleApplyFilters = () => {
    onFiltersChange?.(filters);
    alert('Filters applied');
  };

  return (
    <div className="bg-gray-50 p-6 rounded-lg border border-gray-200 mt-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Filters</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Submission Date Range
          </label>
          <DateRangePicker
            range={filters.submissionDateRange}
            onChange={(range) => handleFilterChange('submissionDateRange', range)}
            placeholder="Select Date Range"
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Department
          </label>
          <Dropdown
            value={filters.department}
            onChange={(value) => handleFilterChange('department', value)}
            options={departmentOptions}
            placeholder="Select"
            fullWidth
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Approver
          </label>
          <FormInput
            value={filters.approver}
            onChange={(value) => handleFilterChange('approver', value)}
            placeholder="Search Name"
            fullWidth
            icon={<FiSearch className="w-4 h-4" />}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Approval Status
          </label>
          <Dropdown
            value={filters.approvalStatus}
            onChange={(value) => handleFilterChange('approvalStatus', value)}
            options={approvalStatusOptions}
            placeholder="Select"
            fullWidth
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Amount Range
          </label>
          <Dropdown
            value={filters.amountRange}
            onChange={(value) => handleFilterChange('amountRange', value)}
            options={amountRangeOptions}
            placeholder="Select"
            fullWidth
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Payment Method
          </label>
          <Dropdown
            value={filters.paymentMethod}
            onChange={(value) => handleFilterChange('paymentMethod', value)}
            options={paymentMethodOptions}
            placeholder="Select"
            fullWidth
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Employee
          </label>
          <FormInput
            value={filters.employee}
            onChange={(value) => handleFilterChange('employee', value)}
            placeholder="Search Name"
            fullWidth
            icon={<FiSearch className="w-4 h-4" />}
          />
        </div>
      </div>

      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="policyViolations"
            checked={filters.policyViolations}
            onChange={(e) => handleFilterChange('policyViolations', e.target.checked)}
            className="w-4 h-4 text-teal-600 bg-gray-50 border-gray-300 rounded focus:ring-teal-500 focus:ring-2"
          />
          <label htmlFor="policyViolations" className="ml-2 text-sm font-medium text-gray-700">
            Policy Violations
          </label>
        </div>

        <div className="flex justify-center gap-5">
          <Button
            onClick={handleReset}
            className="px-10 py-2 rounded-full font-semibold border-2 border-teal-900 text-sm text-teal-900"
          >
            Reset
          </Button>
          <Button
            onClick={handleApplyFilters}
            className="px-10 py-3 rounded-full brand-gradient text-sm text-white"
          >
            Apply Filters
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AllExpenseFiltersSection;
