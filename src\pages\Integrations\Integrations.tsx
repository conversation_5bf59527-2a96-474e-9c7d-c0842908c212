import { useState } from 'react';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { FiSearch } from 'react-icons/fi';
import ManageIntegrationModal from './components/ManageIntegrationModal';
import Pagination from '../../components/Pagination';

interface Integration {
  id: string;
  name: string;
  description: string;
  status: 'connected' | 'disconnected';
}

const itemsPerPage = 4;

const Integrations = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null);
  const [currentPage, setCurrentPage] = useState(1);

  const [integrations, setIntegrations] = useState<Integration[]>([
    {
      id: '1',
      name: 'Accounting System',
      description: 'Connect your accounting system to automate financial data synchronization.',
      status: 'connected',
    },
    {
      id: '2',
      name: 'HR Platform',
      description: 'Integrate your HR platform to streamline employee data management.',
      status: 'disconnected',
    },
    {
      id: '3',
      name: 'Payment Gateway',
      description: 'Enable payment processing through your preferred payment gateway.',
      status: 'connected',
    },
    {
      id: '4',
      name: 'CRM Tool',
      description: 'Sync customer data between your CRM and our platform.',
      status: 'disconnected',
    },
    {
      id: '5',
      name: 'Email Marketing Service',
      description: 'Connect your email marketing service to automate email campaigns.',
      status: 'connected',
    },
  ]);

  const filteredIntegrations = integrations.filter(integration =>
    integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    integration.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredIntegrations.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredIntegrations.length / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleConnect = (id: string) => {
    setIntegrations(prev =>
      prev.map(integration =>
        integration.id === id ? { ...integration, status: 'connected' } : integration
      )
    );
  };

  const handleManage = (id: string) => {
    const integration = integrations.find(item => item.id === id);
    if (integration) {
      setSelectedIntegration(integration);
      setIsModalOpen(true);
    }
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Integrations</h1>
          <Breadcrumb pageName="Integrations" />
        </div>
      </div>

      <div className="relative mb-6">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <FiSearch className="w-5 h-5 text-gray-400" />
        </div>
        <input
          type="text"
          className="bg-gray-100 border border-gray-200 text-gray-700 text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full pl-10 p-2.5"
          placeholder="Search integrations..."
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setCurrentPage(1);
          }}
        />
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full min-w-[640px] mt-2 divide-y divide-gray-200">
            <thead>
              <tr>
                <th className="text-left px-6 py-5 text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Name
                </th>
                <th className="text-left px-6 py-5 text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Description
                </th>
                <th className="text-left px-6 py-5 text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Status
                </th>
                <th className="text-left px-6 py-5 text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100 bg-white">
              {currentItems.map((integration) => (
                <tr key={integration.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 text-sm text-gray-900 font-medium">{integration.name}</td>
                  <td className="px-6 py-4 text-sm text-gray-600 max-w-xs">{integration.description}</td>
                  <td className="px-6 py-4">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        integration.status === 'connected'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {integration.status === 'connected' ? 'Connected' : 'Disconnected'}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    {integration.status === 'connected' ? (
                      <button
                        onClick={() => handleManage(integration.id)}
                        className="text-gray-600 text-sm cursor-pointer font-bold"
                      >
                        Manage
                      </button>
                    ) : (
                      <button
                        onClick={() => handleConnect(integration.id)}
                        className="text-gray-600 text-sm cursor-pointer font-bold"
                      >
                        Connect
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {totalPages > 1 && (
          <div className="px-6 py-4 bg-white border-t border-gray-200">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              siblingCount={1}
            />
          </div>
        )}
      </div>

      <ManageIntegrationModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        integration={selectedIntegration}
      />
    </div>
  );
};

export default Integrations;
