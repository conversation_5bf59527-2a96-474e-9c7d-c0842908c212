import { useState } from 'react';
import { FiCheckCircle } from 'react-icons/fi';
import Modal from '../../../components/Modal';
import FormDropdown from '../../../components/FormDropdown';

interface ManageIntegrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  integration: {
    id: string;
    name: string;
  } | null;
}

const ManageIntegrationModal = ({ isOpen, onClose, integration }: ManageIntegrationModalProps) => {
  const [syncEnabled, setSyncEnabled] = useState(false);
  const [syncFrequency, setSyncFrequency] = useState<'hourly' | 'daily' | 'manual'>('hourly');
  const [selectedOption, setSelectedOption] = useState('');

  if (!isOpen || !integration) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Manage Integration"
      disableBackdropClick
      size="3xl"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div>
            <h3 className="text-gray-500 text-sm font-bold mb-3">Integration Status</h3>
            <div className="flex items-center space-x-2">
              <div className="bg-green-100 p-1 rounded-full">
                <FiCheckCircle className="text-green-500 w-4 h-4" />
              </div>
              <span className="text-gray-700 text-xs font-semibold">Connected</span>
            </div>
          </div>

          <div>
            <h3 className="text-gray-500 text-sm font-bold mb-6">Connection Details</h3>
            <div className="space-y-3 text-xs font-semibold">
              <div>
                <p className="text-gray-700">Connected Account</p>
                <p className="text-gray-500">Tech Solutions Inc.</p>
              </div>
              <div>
                <p className="text-gray-700">Last Connected</p>
                <p className="text-gray-500">July 20, 2025, 10:30 AM</p>
              </div>
            </div>
          </div>

          <div className='mt-18'>
            <button
              className="cursor-pointer bg-gray-100 rounded-full text-gray-800 text-xs font-bold py-2 px-6"
              onClick={() => alert('Reconnecting...')}
            >
              Reconnect
            </button>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <h3 className="text-gray-500 text-sm font-bold mb-3">Synchronization Settings</h3>
            <div className="flex items-center justify-between mb-4">
              <span className="text-gray-700 text-xs font-semibold">Enable Automatic Sync</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={syncEnabled}
                  onChange={() => setSyncEnabled(!syncEnabled)}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500" />
              </label>
            </div>

            <label className="block text-xs font-semibold text-gray-700 mb-2">Sync Frequency</label>
            <div className="flex space-x-2">
              {['hourly', 'daily', 'manual'].map((option) => (
                <button
                  key={option}
                  onClick={() => setSyncFrequency(option as any)}
                  className={`cursor-pointer py-1.5 px-4 text-xs font-medium rounded-md border ${syncFrequency === option
                      ? 'bg-teal-600 text-white border-teal-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'
                    }`}
                >
                  {option.charAt(0).toUpperCase() + option.slice(1)}
                </button>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-gray-500 text-sm font-bold mb-3">Data Mapping</h3>
            <FormDropdown
              label="Select Data to Sync"
              value={selectedOption}
              onChange={setSelectedOption}
              options={['All Data', 'Expenses Only', 'Invoices Only', 'Custom Selection']}
              size="sm"
              required
            />
          </div>

          <div className="flex space-x-3">
            <button
              className="bg-gray-100 rounded-full text-gray-800 text-xs font-bold py-2 px-6 cursor-pointer"
              onClick={() => alert('Testing connection...')}
            >
              Test Connection
            </button>
            <button
              className="bg-gray-100 rounded-full text-gray-800 text-xs font-bold py-2 px-6 cursor-pointer"
              onClick={() => alert('Viewing logs...')}
            >
              View Logs
            </button>
          </div>
        </div>
      </div>

      <div className="mt-8 flex justify-end">
        <button
          onClick={() => alert('Saving changes...')}
          className="brand-gradient text-white text-sm font-medium py-2 px-6 rounded-md cursor-pointer"
        >
          Save Changes
        </button>
      </div>
    </Modal>
  );
};

export default ManageIntegrationModal;