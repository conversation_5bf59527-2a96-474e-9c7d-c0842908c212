const Login = () => {
  return (
      <section className="grid grid-cols-2 gap-x-20 pt-32 relative">

        <section className="h-64 flex items-center justify-center">
          <div className="max-w-xl rounded text-white">
            <img src="/expenso-logo.png" alt="Expenso Logo" className="w-20 h-20 mb-4" />
            <h1 className="font-semibold mb-2 text-4xl mt-6">Sign in to</h1>
            <p className="text-lg font-light my-4">
              Streamline your company’s expense management <br /> in one secure
              platform.
            </p>
            <p className="text-sm font-light">
              Sign in to get started and take control of your <br /> expenses
              today.
            </p>
          </div>
        </section>

        <section className="z-20 mx-auto bg-white rounded-2xl p-12 shadow-lg absolute right-100 top-4/10">
          <h3 className="text-xl font-normal mb-2">
            Welcome to{" "}
            <span className="text-[#0ee6c9] font-semibold">EXPENSO</span>
          </h3>
          <h1 className="text-6xl font-semibold mb-12">Sign in</h1>

          <form
            className="grid gap-8 w-110"
            onSubmit={(e) => e.preventDefault()}
          >
            <div className="grid gap-2">
              <label
                htmlFor="username"
                className="text-sm font-normal text-gray-800"
              >
                Enter your username or email address
              </label>
              <input
                id="username"
                type="text"
                placeholder="Username or email address"
                className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
                required
              />
            </div>

            <div className="grid gap-2">
              <label
                htmlFor="password"
                className="text-sm font-normal text-gray-700"
              >
                Enter your Password
              </label>
              <input
                id="password"
                type="password"
                placeholder="Password"
                className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
                required
              />
            </div>

            <button
              type="submit"
              className="brand-gradient mt-10 rounded-lg text-white py-3 font-semibold hover:opacity-90 transition"
            >
              Sign in
            </button>
          </form>
        </section>

      </section>
  );
};

export default Login;
