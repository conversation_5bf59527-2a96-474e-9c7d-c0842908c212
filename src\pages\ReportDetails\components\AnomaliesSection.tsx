export default function AnomaliesSection() {
  return (
    <div className="space-y-6 h-full">
      {/* Section Header */}
      <div className="bg-teal-50 rounded-xl p-4">
        <h3 className="text-sm font-bold text-gray-500 mb-4">Anomalies</h3>

        {/* Unusual High Transaction */}
        <div className="flex items-start gap-3">
          <div className="pt-1">
            <img src='/warningIcon.svg' className="w-8 h-8 text-orange-400" />
          </div>
          <div>
            <p className="font-semibold text-gray-500 mb-1">Unusual High Transaction</p>
            <p className="text-sm font-medium text-gray-500">
              hotel claim for the employee on Apr 10 is
              <span className="font-semibold text-orange-400">&nbsp;40%</span> above the average
            </p>
          </div>
        </div>
      </div>

      {/* Two Column Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* Budget Utilization */}
        <div className="bg-teal-50 rounded-xl p-4">
          <div className="flex gap-3 items-start">
            <img src='/budgetIcon.svg' className="w-8 h-8 text-green-600" />
            <div>
              <p className="font-semibold text-green-600 mb-1">Budget Utilization</p>
              <p className="text-sm text-gray-600 font-medium">
                They have used
                <span className="font-semibold text-teal-600 font-bold">&nbsp;30%</span> of the allocated current budget
              </p>
            </div>
          </div>
        </div>

        {/* Category Expenditure */}
        <div className="bg-teal-50 rounded-xl p-4">
          <div className="flex gap-3 items-start">
            <img src='/categoryExpenditureIcon.svg' className="w-8 h-8 text-sky-600" />
            <div>
              <p className="font-semibold text-sky-600 mb-1">Category Expenditure</p>
              <p className="text-sm text-gray-600 font-medium">
                Meals spending is
                <span className="font-semibold text-orange-400">&nbsp;18%</span> above average
              </p>
            </div>
          </div>
        </div>

        {/* Average Approval Time */}
        <div className="bg-teal-50 rounded-xl p-4">
          <div className="flex gap-3 items-start">
            <img src='/approvalTimeIcon.svg' className="w-8 h-8 text-teal-600" />
            <div>
              <p className="font-semibold text-teal-600 mb-1">Average Approval Time</p>
              <p className="text-sm text-gray-600 font-medium">
                Your claims are usually approved in
                <span className="font-semibold text-teal-600">&nbsp;1 to 2 days</span>
              </p>
            </div>
          </div>
        </div>


        <div className="bg-teal-50 rounded-xl p-4">
          <div className="flex gap-3 items-start">
            <img src='/policyCompliaceIcon.svg' className="w-8 h-8 text-orange-400" />
            <div>
              <p className="font-semibold text-orange-400 mb-1">Policy Compliance</p>
              <p className="text-sm text-gray-600 font-medium">
                <span className="font-semibold text-orange-400">2</span> Claims in dispute and currently under investigation
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
