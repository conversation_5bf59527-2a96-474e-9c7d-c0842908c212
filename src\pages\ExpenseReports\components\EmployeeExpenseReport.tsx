import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { Button } from '../../../components/Button';
import Pagination from '../../../components/Pagination';

const EmployeeExpenseReport = () => {
    const navigate = useNavigate();
    const [currentPage, setCurrentPage] = useState(1);
    const [disputesCurrentPage, setDisputesCurrentPage] = useState(1);
    const itemsPerPage = 10;
    const disputesPerPage = 5;
    const expenseData = [
        {
            date: 'Jan 03 2025',
            expenseName: 'Client Dinner',
            category: 'Meals & Entertainment',
            amount: 120.00,
            status: 'Approved',
            notes: 'Dinner with ACME team'
        },
        {
            date: 'Jan 05 2025',
            expenseName: 'Travel to Conference',
            category: 'Travel & Transportation',
            amount: 500.00,
            status: 'Approved',
            notes: 'Flight to conference'
        },
        {
            date: 'Jan 07 2025',
            expenseName: 'Hotel Stay',
            category: 'Travel & Transportation',
            amount: 300.00,
            status: 'Approved',
            notes: 'Hotel for conference'
        },
        {
            date: 'Jan 08 2025',
            expenseName: 'Conference Fees',
            category: 'Professional Development',
            amount: 200.00,
            status: 'Approved',
            notes: 'Registration fee'
        },
        {
            date: 'Jan 10 2025',
            expenseName: 'Client Lunch',
            category: 'Meals & Entertainment',
            amount: 80.00,
            status: 'Approved',
            notes: 'Lunch with potential client'
        },
        {
            date: 'Jan 12 2025',
            expenseName: 'Office Supplies',
            category: 'Office Expenses',
            amount: 50.00,
            status: 'Approved',
            notes: 'Stationery and supplies'
        },
        {
            date: 'Jan 15 2025',
            expenseName: 'Client Dinner',
            category: 'Meals & Entertainment',
            amount: 120.00,
            status: 'In Review',
            notes: 'Need itemized receipt'
        },
        {
            date: 'Jan 18 2025',
            expenseName: 'Team Building Event',
            category: 'Team Activities',
            amount: 150.00,
            status: 'Approved',
            notes: 'Team outing'
        },
        {
            date: 'Jan 20 2025',
            expenseName: 'Software Subscription',
            category: 'Technology',
            amount: 100.00,
            status: 'Approved',
            notes: 'Annual subscription'
        },
        {
            date: 'Jan 22 2025',
            expenseName: 'Client Meeting',
            category: 'Meals & Entertainment',
            amount: 70.00,
            status: 'Approved',
            notes: 'Coffee meeting'
        },
        {
            date: 'Jan 25 2025',
            expenseName: 'Travel to Client Site',
            category: 'Travel & Transportation',
            amount: 250.00,
            status: 'Approved',
            notes: 'Train ticket'
        },
        {
            date: 'Jan 28 2025',
            expenseName: 'Project Materials',
            category: 'Project Expenses',
            amount: 80.00,
            status: 'Approved',
            notes: 'Materials for project'
        }
    ];
    const categoryData = [
        { name: 'Travel & Transportation', amount: 1050, color: 'bg-teal-200' },
        { name: 'Meals & Entertainment', amount: 390, color: 'bg-teal-300' },
        { name: 'Professional Development', amount: 200, color: 'bg-teal-200' },
        { name: 'Office Expenses', amount: 50, color: 'bg-teal-200' },
        { name: 'Team Activities', amount: 150, color: 'bg-teal-300' },
        { name: 'Technology', amount: 100, color: 'bg-teal-200' },
        { name: 'Project Expenses', amount: 80, color: 'bg-teal-300' }
    ];
    const disputesData = [
        {
            date: 'Jan 15 2025',
            expense: 'Client Dinner',
            amount: 120.00,
            disputeStatus: 'In Review',
            reasonSummary: 'Need itemized receipt'
        }
    ];

    const totalPages = Math.ceil(expenseData.length / itemsPerPage);
    const disputesTotalPages = Math.ceil(disputesData.length / disputesPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentExpenses = expenseData.slice(startIndex, endIndex);

    const disputesStartIndex = (disputesCurrentPage - 1) * disputesPerPage;
    const disputesEndIndex = disputesStartIndex + disputesPerPage;
    const currentDisputes = disputesData.slice(disputesStartIndex, disputesEndIndex);

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'Approved':
                return 'text-green-600 bg-green-50';
            case 'In Review':
                return 'text-yellow-600 bg-yellow-50';
            case 'Rejected':
                return 'text-red-600 bg-red-50';
            default:
                return 'text-gray-600 bg-gray-50';
        }
    };

    return (
        <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
            
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-8 gap-4">
                <div className="flex items-center gap-4">
                    <button
                        onClick={() => navigate(-1)}
                        className="flex items-center gap-2 text-teal-600 hover:text-teal-700 transition-colors"
                    >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                        Back
                    </button>
                    <h1 className="text-2xl font-bold text-teal-600">Employee Expense Report</h1>
                </div>

                <Button
                    className="bg-gray-700 hover:bg-gray-800 px-6 py-2 rounded text-sm text-white"
                    onClick={() => window.print()}
                >
                    Download
                </Button>
            </div>

            
            <div className="bg-white p-8 rounded-lg shadow-sm">
                
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                    
                    <div>
                        <h2 className="text-lg font-semibold text-gray-600 mb-6">Employee Details</h2>

                        <div className="space-y-4">
                            <div>
                                <p className="text-sm text-gray-500 mb-1">Name</p>
                                <p className="font-semibold text-gray-900">Sophia Clark</p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-500 mb-1">Employee ID</p>
                                <p className="font-semibold text-gray-900">12345</p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-500 mb-1">Department</p>
                                <p className="font-semibold text-gray-900">Marketing</p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-500 mb-1">Designation</p>
                                <p className="font-semibold text-gray-900">Developer</p>
                            </div>
                        </div>
                    </div>

                    
                    <div>
                        <h2 className="text-lg font-semibold text-gray-600 mb-6">Key Summary</h2>

                        <div className="space-y-4">
                            <div>
                                <p className="text-sm text-gray-500 mb-1">Total Expenses</p>
                                <p className="font-semibold text-gray-900">$4,320.00</p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-500 mb-1">Avg. Approval Time</p>
                                <p className="font-semibold text-gray-900">1.6 days</p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-500 mb-1"># of Claims</p>
                                <p className="font-semibold text-gray-900">12</p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-500 mb-1"># of Disputes</p>
                                <p className="font-semibold text-gray-900">1</p>
                            </div>
                        </div>
                    </div>

                    
                    <div>
                        <h2 className="text-lg font-semibold text-gray-600 mb-6">Report Period</h2>

                        <div>
                            <p className="text-sm text-gray-500 mb-1">Period</p>
                            <p className="font-semibold text-gray-900">Jan 1 – Jan 31, 2025</p>
                        </div>
                    </div>
                </div>

                
                <div className="border-t border-gray-200 my-8"></div>

                
                <div>
                    <h2 className="text-lg font-semibold text-gray-600 mb-6">Detailed Expense Table</h2>

                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead>
                                <tr className="border-b border-gray-200">
                                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Date</th>
                                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Expense Name</th>
                                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Category</th>
                                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Amount ($)</th>
                                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Status</th>
                                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {currentExpenses.map((expense, index) => (
                                    <tr key={index} className="border-b border-gray-100">
                                        <td className="py-4 px-4 text-sm text-gray-500">{expense.date}</td>
                                        <td className="py-4 px-4 text-sm font-medium text-gray-900">{expense.expenseName}</td>
                                        <td className="py-4 px-4 text-sm text-gray-600">{expense.category}</td>
                                        <td className="py-4 px-4 text-sm font-medium text-gray-900">{expense.amount.toFixed(2)}</td>
                                        <td className="py-4 px-4">
                                            <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(expense.status)}`}>
                                                {expense.status}
                                            </span>
                                        </td>
                                        <td className="py-4 px-4 text-sm text-gray-500">{expense.notes}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    
                    <div className="mt-6">
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            onPageChange={setCurrentPage}
                        />
                    </div>
                </div>

                
                <div className="border-t border-gray-200 my-8"></div>

                
                <div className="mb-8">
                    <h2 className="text-lg font-semibold text-gray-600 mb-6">Category Breakdown</h2>

                    <div className="bg-gray-50 p-6 rounded-lg">
                        <div className="mb-4">
                            <p className="text-sm text-gray-600 mb-1">Expense Categories</p>
                            <p className="text-3xl font-bold text-gray-900">$4,320.00</p>
                            <p className="text-sm text-green-600">Total +10%</p>
                        </div>

                        
                        <div className="flex items-end justify-between gap-4 h-40 mb-4">
                            {categoryData.map((category, index) => {
                                const maxAmount = Math.max(...categoryData.map(c => c.amount));
                                const height = (category.amount / maxAmount) * 100;

                                return (
                                    <div key={index} className="flex flex-col items-center flex-1">
                                        <div className="w-full flex flex-col justify-end h-32">
                                            <div
                                                className={`${category.color} rounded-t-sm border-t-2 border-teal-600`}
                                                style={{ height: `${height}%` }}
                                            ></div>
                                        </div>
                                        <div className="mt-2 text-center">
                                            <p className="text-xs text-teal-600 font-medium leading-tight">
                                                {category.name.split(' ').map((word, i) => (
                                                    <span key={i} className="block">{word}</span>
                                                ))}
                                            </p>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>

                
                <div>
                    <h2 className="text-lg font-semibold text-gray-600 mb-6">Disputes & Exceptions</h2>

                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead>
                                <tr className="border-b border-gray-200">
                                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Date</th>
                                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Expense</th>
                                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Amount</th>
                                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Dispute Status</th>
                                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Reason Summary</th>
                                </tr>
                            </thead>
                            <tbody>
                                {currentDisputes.length > 0 ? (
                                    currentDisputes.map((dispute, index) => (
                                        <tr key={index} className="border-b border-gray-100">
                                            <td className="py-4 px-4 text-sm text-gray-500">{dispute.date}</td>
                                            <td className="py-4 px-4 text-sm font-medium text-gray-900">{dispute.expense}</td>
                                            <td className="py-4 px-4 text-sm font-medium text-gray-900">{dispute.amount.toFixed(2)}</td>
                                            <td className="py-4 px-4">
                                                <span className="px-3 py-1 text-xs font-medium text-yellow-600 bg-yellow-50 rounded-full">
                                                    {dispute.disputeStatus}
                                                </span>
                                            </td>
                                            <td className="py-4 px-4 text-sm text-gray-500">{dispute.reasonSummary}</td>
                                        </tr>
                                    ))
                                ) : (
                                    <tr>
                                        <td colSpan={5} className="py-8 px-4 text-center text-gray-500">
                                            No disputes or exceptions found
                                        </td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                    </div>

                    
                    {disputesTotalPages > 1 && (
                        <div className="mt-6">
                            <Pagination
                                currentPage={disputesCurrentPage}
                                totalPages={disputesTotalPages}
                                onPageChange={setDisputesCurrentPage}
                            />
                        </div>
                    )}
                </div>

                
                <div className="border-t border-gray-200 my-8"></div>

                
                <div>
                    <h2 className="text-lg font-semibold text-gray-600 mb-6">Approval Log</h2>

                    <div className="max-h-96 overflow-y-auto pr-2">
                        <div className="space-y-6">
                            
                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0 mt-1">
                                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="w-0.5 h-12 bg-gray-200 ml-2.5 mt-2"></div>
                                </div>
                                <div className="flex-1">
                                    <p className="font-medium text-teal-600">Manager approved "Client Dinner"</p>
                                    <p className="text-sm text-gray-500">(Sophia's manager, 1.2 days after submission)</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0 mt-1">
                                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="w-0.5 h-12 bg-gray-200 ml-2.5 mt-2"></div>
                                </div>
                                <div className="flex-1">
                                    <p className="font-medium text-teal-600">Manager approved "Travel to Conference"</p>
                                    <p className="text-sm text-gray-500">(Sophia's manager, 1.5 days after submission)</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0 mt-1">
                                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="w-0.5 h-12 bg-gray-200 ml-2.5 mt-2"></div>
                                </div>
                                <div className="flex-1">
                                    <p className="font-medium text-teal-600">Manager approved "Hotel Stay"</p>
                                    <p className="text-sm text-gray-500">(Sophia's manager, 1.3 days after submission)</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0 mt-1">
                                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="w-0.5 h-12 bg-gray-200 ml-2.5 mt-2"></div>
                                </div>
                                <div className="flex-1">
                                    <p className="font-medium text-teal-600">Manager approved "Conference Fees"</p>
                                    <p className="text-sm text-gray-500">(Sophia's manager, 1.1 days after submission)</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0 mt-1">
                                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="w-0.5 h-12 bg-gray-200 ml-2.5 mt-2"></div>
                                </div>
                                <div className="flex-1">
                                    <p className="font-medium text-teal-600">Manager approved "Client Lunch"</p>
                                    <p className="text-sm text-gray-500">(Sophia's manager, 1.4 days after submission)</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0 mt-1">
                                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="w-0.5 h-12 bg-gray-200 ml-2.5 mt-2"></div>
                                </div>
                                <div className="flex-1">
                                    <p className="font-medium text-teal-600">Manager approved "Office Supplies"</p>
                                    <p className="text-sm text-gray-500">(Sophia's manager, 1.0 days after submission)</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0 mt-1">
                                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="w-0.5 h-12 bg-gray-200 ml-2.5 mt-2"></div>
                                </div>
                                <div className="flex-1">
                                    <p className="font-medium text-teal-600">Manager approved "Team Building Event"</p>
                                    <p className="text-sm text-gray-500">(Sophia's manager, 1.6 days after submission)</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0 mt-1">
                                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="w-0.5 h-12 bg-gray-200 ml-2.5 mt-2"></div>
                                </div>
                                <div className="flex-1">
                                    <p className="font-medium text-teal-600">Manager approved "Software Subscription"</p>
                                    <p className="text-sm text-gray-500">(Sophia's manager, 0.8 days after submission)</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0 mt-1">
                                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="w-0.5 h-12 bg-gray-200 ml-2.5 mt-2"></div>
                                </div>
                                <div className="flex-1">
                                    <p className="font-medium text-teal-600">Manager approved "Client Meeting"</p>
                                    <p className="text-sm text-gray-500">(Sophia's manager, 1.2 days after submission)</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0 mt-1">
                                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="w-0.5 h-12 bg-gray-200 ml-2.5 mt-2"></div>
                                </div>
                                <div className="flex-1">
                                    <p className="font-medium text-teal-600">Manager approved "Travel to Client Site"</p>
                                    <p className="text-sm text-gray-500">(Sophia's manager, 1.1 days after submission)</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <div className="flex-shrink-0 mt-1">
                                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                                <div className="flex-1">
                                    <p className="font-medium text-teal-600">Manager approved "Project Materials"</p>
                                    <p className="text-sm text-gray-500">(Sophia's manager, 0.9 days after submission)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default EmployeeExpenseReport;