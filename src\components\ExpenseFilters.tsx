import { useState } from "react";
import type { DateRange } from "react-day-picker";
import Dropdown from "./Dropdown";
import DateRangePicker from "./DateRangePicker";
import SearchInput from "./SearchInput";

interface ExpenseFiltersProps {
  onApplyFilters: (filters: ExpenseFilterValues) => void;
  onReset: () => void;
}

export interface ExpenseFilterValues {
  dateRange: DateRange | undefined;
  department: string;
  approver: string;
  approvalStatus: string;
  amountRange: string;
  paymentMethod: string;
  employee: string;
  policyViolations: boolean;
}

const defaultFilters: ExpenseFilterValues = {
  dateRange: undefined,
  department: "",
  approver: "",
  approvalStatus: "",
  amountRange: "",
  paymentMethod: "",
  employee: "",
  policyViolations: false,
};

const departmentOptions = ["Finance", "Marketing", "Engineering", "HR", "Sales"];
const approvalStatusOptions = ["Approved", "Pending", "Rejected"];
const amountRangeOptions = ["$0-$100", "$100-$500", "$500-$1000", "$1000+"];
const paymentMethodOptions = ["Credit Card", "Cash", "Bank Transfer", "Check"];

export default function ExpenseFilters({ onApplyFilters, onReset }: ExpenseFiltersProps) {
  const [filters, setFilters] = useState<ExpenseFilterValues>(defaultFilters);

  const handleReset = () => {
    setFilters(defaultFilters);
    onReset();
  };

  const handleApplyFilters = () => {
    onApplyFilters(filters);
  };

  return (
    <div className="bg-gray-50 p-6 rounded-lg">
      <h2 className="text-lg font-medium text-gray-800 mb-4">Filters</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    
        <div className="space-y-2">
          <label className="block text-sm text-gray-600">Submission Date Range</label>
          <DateRangePicker
            range={filters.dateRange}
            onChange={(range) => setFilters({ ...filters, dateRange: range })}
            placeholder="Enter Date Range"
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm text-gray-600">Department</label>
          <Dropdown
            value={filters.department}
            onChange={(value) => setFilters({ ...filters, department: value })}
            options={departmentOptions}
            label="Department"
            placeholder="Select"
            fullWidth
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm text-gray-600">Approver</label>
          <SearchInput
            value={filters.approver}
            onChange={(value) => setFilters({ ...filters, approver: value })}
            placeholder="Search Name"
            fullWidth
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm text-gray-600">Approval Status</label>
          <Dropdown
            value={filters.approvalStatus}
            onChange={(value) => setFilters({ ...filters, approvalStatus: value })}
            options={approvalStatusOptions}
            label="Approval Status"
            placeholder="Select"
            fullWidth
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm text-gray-600">Amount Range</label>
          <Dropdown
            value={filters.amountRange}
            onChange={(value) => setFilters({ ...filters, amountRange: value })}
            options={amountRangeOptions}
            label="Amount Range"
            placeholder="Select"
            fullWidth
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm text-gray-600">Payment Method</label>
          <Dropdown
            value={filters.paymentMethod}
            onChange={(value) => setFilters({ ...filters, paymentMethod: value })}
            options={paymentMethodOptions}
            label="Payment Method"
            placeholder="Select"
            fullWidth
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm text-gray-600">Employee</label>
          <SearchInput
            value={filters.employee}
            onChange={(value) => setFilters({ ...filters, employee: value })}
            placeholder="Search Name"
            fullWidth
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm text-gray-600">Approval Status</label>
          <Dropdown
            value={filters.approvalStatus}
            onChange={(value) => setFilters({ ...filters, approvalStatus: value })}
            options={approvalStatusOptions}
            label="Approval Status"
            placeholder="Select"
            fullWidth
          />
        </div>
      </div>
      
      <div className="mt-6 flex flex-wrap items-center justify-between">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="policyViolations"
            checked={filters.policyViolations}
            onChange={(e) => setFilters({ ...filters, policyViolations: e.target.checked })}
            className="h-4 w-4 rounded border-gray-300 text-teal-600 focus:ring-teal-500"
          />
          <label htmlFor="policyViolations" className="ml-2 text-sm text-gray-700">
            Policy Violations
          </label>
        </div>
        
        <div className="flex gap-3 mt-4 sm:mt-0">
          <button
            onClick={handleReset}
            className="px-6 py-2 border border-gray-300 rounded-full text-gray-700 bg-white hover:bg-gray-50 text-sm font-medium"
          >
            Reset
          </button>
          <button
            onClick={handleApplyFilters}
            className="px-6 py-2 bg-teal-700 text-white rounded-full hover:bg-teal-800 text-sm font-medium"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
}