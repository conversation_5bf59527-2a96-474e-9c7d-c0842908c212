import { useState } from "react";
import ExpenseFilters, { type ExpenseFilterValues } from "./ExpenseFilters";
import ExpenseFilterButton from "./ExpenseFilterButton";

export default function ExpenseFiltersExample() {
  const [activeFilters, setActiveFilters] = useState<ExpenseFilterValues | null>(null);

  const handleApplyFilters = (filters: ExpenseFilterValues) => {
    setActiveFilters(filters);
    console.log("Applied filters:", filters);
  };

  const handleReset = () => {
    setActiveFilters(null);
    console.log("Filters reset");
  };

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-xl font-semibold">Expense Reports</h1>
        <ExpenseFilterButton onApplyFilters={handleApplyFilters} />
      </div>

      <div className="mb-8">
        <h2 className="text-lg font-medium mb-2">Filter Component Demo:</h2>
        <ExpenseFilters 
          onApplyFilters={handleApplyFilters} 
          onReset={handleReset} 
        />
      </div>

      {activeFilters && (
        <div className="mt-4 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-medium mb-2">Active Filters:</h3>
          <pre className="text-xs overflow-auto bg-white p-2 rounded">
            {JSON.stringify(activeFilters, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}