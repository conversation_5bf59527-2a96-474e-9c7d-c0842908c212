import { useNavigate } from 'react-router-dom';
import { Button } from '../../components/Button';
import SingleExpenseTable from './components/SingleExpenseTable';
import ActivityInsightsFeed from './components/ActivityInsightsFeed';
import AnomaliesSection from './components/AnomaliesSection';
import SingleActiveDisputesTable from './components/SingleActiveDisputesTable';
import { sampleDisputes } from '../../mockData/mockData';
import SinglePastExpensesTable from './components/SinglePastExpensesTable';


const ReportDetails = () => {
  const navigate = useNavigate();

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Expense Reports</h1>
          <div className="flex items-center">
            <span
              className="cursor-pointer hover:text-teal-600 transition-colors text-sm text-gray-500 font-semibold"
              onClick={() => navigate('/')}
            >
              Dashboard
            </span>
            <span className="mx-2 text-sm text-gray-500">/</span>
            <span
              className="cursor-pointer hover:text-teal-600 transition-colors text-sm text-gray-500 font-semibold"
              onClick={() => navigate('/expense-reports')}
            >
              Expense Reports
            </span>
            <span className="mx-2 text-sm text-gray-500">/</span>
            <span className="text-sm text-teal-600 font-semibold">Report Details</span>
          </div>
        </div>

        <Button
          className="brand-gradient px-8 py-3 rounded-sm text-sm text-white"
          onClick={() => navigate('/expense-reports/employee-report')}
        >
          Export
        </Button>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm">

        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-8">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Olivia Bennett</h1>
            <p className="text-gray-500 font-semibold mb-1">Marketing Department</p>
            <p className="text-sm text-gray-500">
              Employee ID: 12345 | Latest Submission Date: 2024-03-15
            </p>
          </div>

          <div className="flex-[1.5] w-full sm:w-auto sm:max-w-xl mt-4 sm:mt-0">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-500 h-2 rounded-full" style={{ width: '70%' }}></div>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-xs text-gray-500 mt-2">Usage Limit</span>
              <p className="text-md font-semibold text-gray-600 mt-2">$350.60 / $50000</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-sm font-semibold text-gray-500 mb-2">Total Amount</h3>
            <p className="text-2xl font-bold text-gray-600">$12,500.00</p>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-sm font-semibold text-gray-500 mb-2">Current Status</h3>
            <p className="text-2xl font-bold text-gray-600">Approved</p>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-sm font-semibold text-gray-500 mb-2">Average Spending</h3>
            <p className="text-2xl font-bold text-gray-600">$230.00</p>
            <p className="text-sm text-orange-500 mt-1">-5%</p>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-sm font-semibold text-gray-500 mb-2">Policy Compliance Summary</h3>
            <p className="text-2xl font-bold text-gray-600">Compliant</p>
            <p className="text-sm text-gray-400 mt-1">as of recent activities</p>
          </div>
        </div>
      </div>

      <SingleExpenseTable
        title="Latest Expense Requests"
        expenses={[
          {
            id: "1",
            type: "Multiple Expenses",
            description: "Client meeting & travel",
            date: "2024-07-25",
            amount: "$1,050.00",
            status: "Check Done",
            approval: "Approved",
          },
          {
            id: "2",
            type: "Business Lunch",
            description: "Lunch with sales team",
            date: "2024-07-24",
            amount: "$320.00",
            status: "Rejected",
            approval: "Rejected",
          },
        ]}
      />

      <div className="bg-white p-6 rounded-lg shadow-sm mt-6">
        <h2 className="text-lg font-semibold text-gray-600 mb-6">Activity & Insights</h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-stretch">
          <ActivityInsightsFeed />
          <AnomaliesSection />
        </div>
      </div>

      <SingleActiveDisputesTable
        title="Active Disputes"
        disputes={sampleDisputes}
      />

      <SinglePastExpensesTable />
    </div>
  );
};

export default ReportDetails;
