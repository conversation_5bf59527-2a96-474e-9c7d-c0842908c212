import { useLocation, useNavigate } from 'react-router-dom';

interface BreadcrumbItem {
  label: string;
  path?: string;
}

interface BreadcrumbProps {
  pageName?: string;
  items?: BreadcrumbItem[];
}

const Breadcrumb = ({ pageName, items }: BreadcrumbProps) => {
  const location = useLocation();
  const navigate = useNavigate();

  if (location.pathname === '/login' || location.pathname === '/signup') {
    return null;
  }

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  if (items && items.length > 0) {
    return (
      <nav className="text-sm text-gray-500 mb-2 font-semibold">
        {items.map((item, index) => (
          <span key={index}>
            {item.path ? (
              <span 
                className="cursor-pointer hover:text-teal-600 transition-colors"
                onClick={() => handleNavigation(item.path!)}
              >
                {item.label}
              </span>
            ) : (
              <span className="text-teal-600">
                {item.label}
              </span>
            )}
            {index < items.length - 1 && <span className="mx-2">/</span>}
          </span>
        ))}
      </nav>
    );
  }

  return (
    <nav className="text-sm text-gray-500 mb-2 font-semibold">
      <span 
        className="cursor-pointer hover:text-teal-600 transition-colors"
        onClick={() => handleNavigation('/')}
      >
        Dashboard
      </span>
      <span className="mx-2">/</span>
      <span className="text-teal-600">
        {pageName}
      </span>
    </nav>
  );
};

export default Breadcrumb;