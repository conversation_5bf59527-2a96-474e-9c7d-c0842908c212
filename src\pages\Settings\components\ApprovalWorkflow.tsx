import React, { useState, useEffect } from 'react';

interface ApprovalLevel {
  id: string;
  name: string;
  threshold: string;
  approvers: string;
}

interface ApprovalLimit {
  id: string;
  category: string;
  limit: string;
  period: string;
}

interface AutoApprovalSettings {
  enabled: boolean;
  threshold: string;
}

interface ApprovalWorkflowData {
  approvalLevels: ApprovalLevel[];
  approvalLimits: ApprovalLimit[];
  autoApproval: AutoApprovalSettings;
}

class ApprovalWorkflowAPI {
  static async getApprovalWorkflow(): Promise<ApprovalWorkflowData> {
    return {
      approvalLevels: [],
      approvalLimits: [],
      autoApproval: { enabled: true, threshold: '$100' }
    };
  }

  static async updateApprovalLevel(levelId: string, data: Partial<ApprovalLevel>): Promise<ApprovalLevel> {    
    console.log('API: Update approval level', levelId, data);
    return { id: levelId, ...data } as ApprovalLevel;
  }

  static async updateApprovalLimit(limitId: string, data: Partial<ApprovalLimit>): Promise<ApprovalLimit> {
    console.log('API: Update approval limit', limitId, data);
    return { id: limitId, ...data } as ApprovalLimit;
  }

  static async updateAutoApproval(settings: AutoApprovalSettings): Promise<AutoApprovalSettings> {
    console.log('API: Update auto approval', settings);
    return settings;
  }

  static async createApprovalLevel(data: Omit<ApprovalLevel, 'id'>): Promise<ApprovalLevel> {
    console.log('API: Create approval level', data);
    return { id: Date.now().toString(), ...data };
  }

  static async createApprovalLimit(data: Omit<ApprovalLimit, 'id'>): Promise<ApprovalLimit> {
    console.log('API: Create approval limit', data);
    return { id: Date.now().toString(), ...data };
  }
}

const ApprovalWorkflow: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const [approvalLevels, setApprovalLevels] = useState<ApprovalLevel[]>([]);
  const [approvalLimits, setApprovalLimits] = useState<ApprovalLimit[]>([]);
  const [autoApprovalSettings, setAutoApprovalSettings] = useState<AutoApprovalSettings>({
    enabled: true,
    threshold: '$100'
  });

  useEffect(() => {
    loadApprovalWorkflowData();
  }, []);

  const loadApprovalWorkflowData = async (): Promise<void> => {
    try {
      setLoading(true);
      const data = await ApprovalWorkflowAPI.getApprovalWorkflow();
      setApprovalLevels(data.approvalLevels);
      setApprovalLimits(data.approvalLimits);
      setAutoApprovalSettings(data.autoApproval);
    } catch (err) {
      setError('Failed to load approval workflow data');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleLevelUpdate = async (levelId: string, field: keyof Omit<ApprovalLevel, 'id'>, value: string): Promise<void> => {
    try {
      await ApprovalWorkflowAPI.updateApprovalLevel(levelId, { [field]: value });
      setApprovalLevels(prev => 
        prev.map(level => level.id === levelId ? { ...level, [field]: value } : level)
      );
    } catch (err) {
      console.error('Error updating approval level:', err);
      setError('Failed to update approval level');
    }
  };

  const handleLimitUpdate = async (limitId: string, field: keyof Omit<ApprovalLimit, 'id'>, value: string): Promise<void> => {
    try {
      await ApprovalWorkflowAPI.updateApprovalLimit(limitId, { [field]: value });
      setApprovalLimits(prev => 
        prev.map(limit => limit.id === limitId ? { ...limit, [field]: value } : limit)
      );
    } catch (err) {
      console.error('Error updating approval limit:', err);
      setError('Failed to update approval limit');
    }
  };

  const handleAutoApprovalToggle = async (enabled: boolean): Promise<void> => {
    try {
      const newSettings = { ...autoApprovalSettings, enabled };
      await ApprovalWorkflowAPI.updateAutoApproval(newSettings);
      setAutoApprovalSettings(newSettings);
    } catch (err) {
      console.error('Error updating auto approval:', err);
      setError('Failed to update auto approval settings');
    }
  };

  const handleAutoApprovalThresholdChange = async (threshold: string): Promise<void> => {
    try {
      const newSettings = { ...autoApprovalSettings, threshold };
      await ApprovalWorkflowAPI.updateAutoApproval(newSettings);
      setAutoApprovalSettings(newSettings);
    } catch (err) {
      console.error('Error updating auto approval threshold:', err);
      setError('Failed to update auto approval threshold');
    }
  };

  const handleAddApprovalLevel = async (): Promise<void> => {
    try {
      const newLevel = await ApprovalWorkflowAPI.createApprovalLevel({
        name: 'New Approval Level',
        threshold: '$0',
        approvers: 'Manager'
      });
      setApprovalLevels(prev => [...prev, newLevel]);
    } catch (err) {
      console.error('Error creating approval level:', err);
      setError('Failed to create approval level');
    }
  };

  const handleAddApprovalLimit = async (): Promise<void> => {
    try {
      const newLimit = await ApprovalWorkflowAPI.createApprovalLimit({
        category: 'New Category',
        limit: '$0',
        period: 'Monthly'
      });
      setApprovalLimits(prev => [...prev, newLimit]);
    } catch (err) {
      console.error('Error creating approval limit:', err);
      setError('Failed to create approval limit');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-slate-600">Loading approval workflow...</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
          <button 
            onClick={() => setError(null)}
            className="ml-2 text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}

      <div>
        <h2 className="text-xl text-left font-semibold text-slate-800 mb-1">
          Approval Workflow
        </h2>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-base text-left font-medium text-slate-800 mb-4">
            Approval Levels
          </h3>
          <div className="bg-white rounded-lg border border-slate-200 p-8">
            {approvalLevels.length === 0 ? (
              <div className="text-left text-slate-500">

                <p className="text-sm">No approval levels configured yet.</p>
                <button 
                  onClick={handleAddApprovalLevel}
                  className="mt-3 text-teal-600 hover:text-teal-700 text-sm font-medium"
                >
                  + Add Approval Level
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {approvalLevels.map((level) => (
                  <div key={level.id} className="border border-slate-200 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Level Name
                        </label>
                        <input
                          type="text"
                          value={level.name}
                          onChange={(e) => handleLevelUpdate(level.id, 'name', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Threshold Amount
                        </label>
                        <input
                          type="text"
                          value={level.threshold}
                          onChange={(e) => handleLevelUpdate(level.id, 'threshold', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Approvers
                        </label>
                        <input
                          type="text"
                          value={level.approvers}
                          onChange={(e) => handleLevelUpdate(level.id, 'approvers', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
                        />
                      </div>
                    </div>
                  </div>
                ))}
                <button 
                  onClick={handleAddApprovalLevel}
                  className="text-teal-600 hover:text-teal-700 text-sm font-medium"
                >
                  + Add Another Level
                </button>
              </div>
            )}
          </div>
        </div>

        <div>
          <h3 className="text-base text-left font-medium text-slate-800 mb-4">
            Approval Limits
          </h3>
          <div className="bg-white rounded-lg border border-slate-200 p-8">
            {approvalLimits.length === 0 ? (
              <div className="text-left text-slate-500">

                <p className="text-sm">No approval limits configured yet.</p>
                <button 
                  onClick={handleAddApprovalLimit}
                  className="mt-3 text-teal-600 hover:text-teal-700 text-sm font-medium"
                >
                  + Add Approval Limit
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {approvalLimits.map((limit) => (
                  <div key={limit.id} className="border border-slate-200 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Category
                        </label>
                        <input
                          type="text"
                          value={limit.category}
                          onChange={(e) => handleLimitUpdate(limit.id, 'category', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Limit Amount
                        </label>
                        <input
                          type="text"
                          value={limit.limit}
                          onChange={(e) => handleLimitUpdate(limit.id, 'limit', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Period
                        </label>
                        <input
                          type="text"
                          value={limit.period}
                          onChange={(e) => handleLimitUpdate(limit.id, 'period', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
                        />
                      </div>
                    </div>
                  </div>
                ))}
                <button 
                  onClick={handleAddApprovalLimit}
                  className="text-teal-600 hover:text-teal-700 text-sm font-medium"
                >
                  + Add Another Limit
                </button>
              </div>
            )}
          </div>
        </div>

        <div>
          <h3 className="text-base text-left font-medium text-slate-800 mb-2">
            Auto-Approval
          </h3>
          <p className="text-sm text-left text-slate-600 mb-4">
            Enable or disable auto-approval for reports below a certain threshold.
          </p>
          
          <div className="bg-white rounded-lg border border-slate-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium text-slate-700">
                Enable Auto-Approval
              </span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={autoApprovalSettings.enabled}
                  onChange={(e) => handleAutoApprovalToggle(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-600"></div>
              </label>
            </div>
            
            {autoApprovalSettings.enabled && (
              <div className="max-w-md">
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Auto-Approval Threshold
                </label>
                <input
                  type="text"
                  value={autoApprovalSettings.threshold}
                  onChange={(e) => handleAutoApprovalThresholdChange(e.target.value)}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter threshold amount"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApprovalWorkflow;