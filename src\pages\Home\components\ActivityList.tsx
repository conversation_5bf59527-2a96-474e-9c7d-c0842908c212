import React, { useState } from "react";
import Pagination from "../../../components/Pagination";
import { recentActivity } from "../../../mockData/homeMockData";

const ITEMS_PER_PAGE = 5;

const ActivityList: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(recentActivity.length / ITEMS_PER_PAGE);

  const paginatedActivities = recentActivity.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6 border border-gray-100 flex flex-col h-full">

      <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-4 sm:mb-6">
        Recent Activity
      </h3>

      <div className="flex-grow overflow-y-auto">
        <table className="w-full min-w-[640px]">
          <thead>
            <tr className="border-b border-gray-200">
              {["Activity", "Details", "Date", "Actions"].map((label) => (
                <th
                  key={label}
                  className="text-left py-3 px-2 sm:px-4 font-bold text-gray-700 text-xs sm:text-sm"
                >
                  {label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {paginatedActivities.map((activity) => (
              <tr
                key={activity.id}
                className="border-b border-gray-100 hover:bg-gray-50"
              >
                <td className="py-3 px-2 sm:px-4 text-gray-900 text-xs sm:text-sm font-medium">
                  {activity.title}
                </td>
                <td className="py-3 px-2 sm:px-4 text-gray-600 text-xs sm:text-sm">
                  Submitted by {activity.submittedBy}
                </td>
                <td className="py-3 px-2 sm:px-4 text-gray-600 text-xs sm:text-sm">
                  {activity.date}
                </td>
                <td className="py-3 px-2 sm:px-4">
                  <button
                    onClick={() => alert("View activity")}
                    className="text-teal-600 hover:text-teal-800 font-bold text-xs sm:text-sm cursor-pointer"
                  >
                    view
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="mt-4">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </div>
    </div>
  );
};

export default ActivityList;
