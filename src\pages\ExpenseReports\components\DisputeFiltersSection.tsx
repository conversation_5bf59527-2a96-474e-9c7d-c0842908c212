import { useState } from 'react';
import Dropdown from '../../../components/Dropdown';
import DateRangePicker from '../../../components/DateRangePicker';
import type { DateRange } from 'react-day-picker';

interface DisputeFiltersSectionProps {
  onFiltersChange?: (filters: any) => void;
}

const DisputeFiltersSection = ({ onFiltersChange }: DisputeFiltersSectionProps) => {
    const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [filters, setFilters] = useState({
    dateRange: '',
    expenseType: '',
    department: '',
    status: '',
    approvalStatus: '',
  });

  const expenseTypeOptions = ['Travel', 'Meals', 'Office Supplies', 'Equipment', 'Training', 'Entertainment'];
  const departmentOptions = ['HR', 'Finance', 'IT', 'Marketing', 'Sales'];
  const statusOptions = ['Open', 'In Progress', 'Resolved', 'Closed'];
  const approvalStatusOptions = ['Pending', 'Approved', 'Rejected', 'Under Review'];

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

    const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    onFiltersChange?.({
      ...filters,
      dateRange: range,
    });
  };

  return (
    <div className="p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div>
          <DateRangePicker
            range={dateRange}
            onChange={handleDateRangeChange}
            placeholder="Date Range"
            className="w-full"
          />
        </div>

        <div>
          <Dropdown
            value={filters.expenseType}
            onChange={(value) => handleFilterChange('expenseType', value)}
            options={expenseTypeOptions}
            placeholder="Expense Type"
            fullWidth
          />
        </div>

        <div>
          <Dropdown
            value={filters.department}
            onChange={(value) => handleFilterChange('department', value)}
            options={departmentOptions}
            placeholder="Department"
            fullWidth
          />
        </div>

        <div>
          <Dropdown
            value={filters.status}
            onChange={(value) => handleFilterChange('status', value)}
            options={statusOptions}
            placeholder="Status"
            fullWidth
          />
        </div>

        <div>
          <Dropdown
            value={filters.approvalStatus}
            onChange={(value) => handleFilterChange('approvalStatus', value)}
            options={approvalStatusOptions}
            placeholder="Approval Status"
            fullWidth
          />
        </div>
      </div>
    </div>
  );
};

export default DisputeFiltersSection;