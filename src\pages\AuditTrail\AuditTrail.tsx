import { useState } from 'react';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { FiSearch } from 'react-icons/fi';
import Dropdown from '../../components/Dropdown';
import Pagination from '../../components/Pagination';

interface AuditLogEntry {
  timestamp: string;
  timeDetail: string;
  user: string;
  actionType: string;
  objectItem: string;
  description: string;
  ipAddress: string;
  status: 'Success' | 'Failure';
}

const itemsPerPage = 4;

const AuditTrail = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [actionType, setActionType] = useState('');
  const [department, setDepartment] = useState('');
  const [role, setRole] = useState('');
  const [approvalStatus, setApprovalStatus] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  const auditLogs: AuditLogEntry[] = [
    {
      timestamp: '07/26/2024',
      timeDetail: '14:30:00',
      user: '<PERSON>',
      actionType: 'User Login',
      objectItem: 'N/A',
      description: 'Successful login',
      ipAddress: '*************',
      status: 'Success'
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '14:45:15',
      user: 'Olivia Bennett',
      actionType: 'Report Generated',
      objectItem: 'Report #12345',
      description: 'Generated sales report for Q2',
      ipAddress: '*************',
      status: 'Success'
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '15:00:20',
      user: 'Ethan Harper',
      actionType: 'User Logout',
      objectItem: 'N/A',
      description: 'User logged out',
      ipAddress: '*************',
      status: 'Success'
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '15:15:30',
      user: 'Liam Carter',
      actionType: 'Data Update',
      objectItem: 'Customer #67890',
      description: 'Changed: Max Amount from $50 to $60',
      ipAddress: '*************',
      status: 'Success'
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '15:30:45',
      user: 'Sophia Evans',
      actionType: 'System Error',
      objectItem: 'N/A',
      description: 'Failed to process payment transaction',
      ipAddress: '*************',
      status: 'Failure'
    },
  ];

  const filteredLogs = auditLogs.filter(log =>
    log.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.actionType.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalPages = Math.ceil(filteredLogs.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentLogs = filteredLogs.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Audit Trail</h1>
          <Breadcrumb pageName="Audit Trail" />
        </div>
      </div>

      <div className="relative mb-6">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <FiSearch className="w-5 h-5 text-gray-400" />
        </div>
        <input
          type="text"
          className="bg-gray-100 border border-gray-200 text-gray-700 text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full pl-10 p-2.5"
          placeholder="Search audit logs..."
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setCurrentPage(1);
          }}
        />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Dropdown
          label="Action/Event Type"
          value={actionType}
          onChange={setActionType}
          options={['User Login', 'User Logout', 'Report Generated', 'Data Update', 'System Error']}
          placeholder="Action/Event Type"
          fullWidth
        />
        <Dropdown
          label="Department"
          value={department}
          onChange={setDepartment}
          options={['Finance', 'HR', 'IT', 'Sales']}
          placeholder="Department"
          fullWidth
        />
        <Dropdown
          label="Role"
          value={role}
          onChange={setRole}
          options={['Admin', 'Manager', 'User']}
          placeholder="Role"
          fullWidth
        />
        <Dropdown
          label="Approval Status"
          value={approvalStatus}
          onChange={setApprovalStatus}
          options={['Approved', 'Pending', 'Rejected']}
          placeholder="Approval Status"
          fullWidth
        />
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Timestamp</th>
                <th className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">User</th>
                <th className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Action/Event Type</th>
                <th className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Object/Item</th>
                <th className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Details/Description</th>
                <th className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">IP Address</th>
                <th className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentLogs.map((log, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{log.timestamp}</div>
                    <div className="text-sm text-gray-500">{log.timeDetail}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{log.user}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{log.actionType}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{log.objectItem}</td>
                  <td className="px-6 py-4 text-sm text-gray-900">{log.description}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{log.ipAddress}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      log.status === 'Success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {log.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {totalPages > 1 && (
          <div className="px-6 py-4 bg-white border-t border-gray-200">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              siblingCount={1}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default AuditTrail;
