import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { Button } from '../../components/Button';
import Pagination from '../../components/Pagination';
import { FiSearch } from 'react-icons/fi';
import { policyData } from '../../mockData/policyData';

const itemsPerPage = 5;

const PolicyManagement = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredPolicies = policyData.filter(policy =>
    policy.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    policy.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentPolicies = filteredPolicies.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredPolicies.length / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleAddPolicy = () => {
    navigate('/policies/add');
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Policy Management</h1>
          <Breadcrumb pageName="Policy Management" />
        </div>
        <Button
          className="brand-gradient px-8 py-3 rounded-sm text-sm text-white"
          onClick={handleAddPolicy}
        >
          + Add Policy
        </Button>
      </div>

      <div className="relative mb-6">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <FiSearch className="w-5 h-5 text-gray-400" />
        </div>
        <input
          type="text"
          className="bg-gray-100 border border-gray-200 text-gray-700 text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full pl-10 p-2.5"
          placeholder="Search policies"
          value={searchQuery}
          onChange={handleSearch}
        />
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="">
              <tr>
                <th scope="col" className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Policy Name
                </th>
                <th scope="col" className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Description
                </th>
                <th scope="col" className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Applicable To
                </th>
                <th scope="col" className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Last Modified
                </th>
                <th scope="col" className="px-6 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentPolicies.map((policy) => (
                <tr key={policy.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-bold text-teal-700">{policy.name}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-600">{policy.description}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600">{policy.applicableTo}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-xs font-semibold text-gray-600">{policy.lastModified}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-1 sm:gap-2">
                      <button
                        className="text-gray-700 text-xs font-bold cursor-pointer"
                        onClick={() => alert("Policy view")}
                      >
                        View
                      </button>
                      <span className="text-gray-700">/</span>
                      <button
                        onClick={() => alert("Policy edit")}
                        className="text-gray-700 text-xs font-bold cursor-pointer"
                      >
                        Edit
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {totalPages > 1 && (
          <div className="px-6 py-4 bg-white border-t border-gray-200">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              siblingCount={1}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default PolicyManagement;