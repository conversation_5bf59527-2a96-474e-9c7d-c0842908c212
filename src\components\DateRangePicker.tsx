import { useState } from "react";
import { DayPicker } from "react-day-picker";
import type { DateRange } from "react-day-picker";
import "react-day-picker/dist/style.css";
import { format } from "date-fns";

interface DateRangePickerProps {
  range: DateRange | undefined;
  onChange: (range: DateRange | undefined) => void;
  placeholder?: string;
  className?: string;
}

export default function DateRangePicker({
  range,
  onChange,
  placeholder = "Enter Date Range",
  className = "",
}: DateRangePickerProps) {
  const [showCalendar, setShowCalendar] = useState(false);

  const formatRange = (range: DateRange | undefined) => {
    if (!range || !range.from) return placeholder;

    if (!range.to) {
      return format(range.from, "dd/MM/yyyy");
    }

    return `${format(range.from, "dd/MM/yyyy")} - ${format(range.to, "dd/MM/yyyy")}`;
  };

  return (
    <div
      tabIndex={0}
      onBlur={(e) => {
        if (!e.currentTarget.contains(e.relatedTarget)) {
          setShowCalendar(false);
        }
      }}
      className="relative inline-block focus:outline-none w-full"
    >
      <button
        type="button"
        onClick={() => setShowCalendar((prev) => !prev)}
        className={`cursor-pointer flex items-center justify-between gap-8 px-6 py-3 rounded-full bg-white shadow-sm transition text-sm w-full ${className}`}
      >
        <span className={`${range?.from ? "text-gray-700" : "text-gray-500"}`}>
          {formatRange(range)}
        </span>
        <img
          src="/calenderIcon.svg"
          alt="calender"
          className="h-4"
        />
      </button>


      {showCalendar && (
        <div className="absolute z-20 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg p-4">
          <DayPicker
            mode="range"
            selected={range}
            onSelect={onChange}
            numberOfMonths={2}
            modifiersClassNames={{
              selected: "bg-teal-600 text-white",
              today: "text-teal-700 font-semibold",
            }}
            className="font-sans text-sm"
          />
        </div>
      )}
    </div>
  );
}