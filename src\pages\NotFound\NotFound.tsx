const NotFound = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 py-8">
      <div className="max-w-md w-full text-center">
        <div className="mb-8 flex justify-center">
          <div className="w-screen h-screen sm:w-80 sm:h-80 md:w-96 md:h-96 flex items-center justify-center">
            <img
              src='404.svg'
              alt={"404"}
              className="max-w-screen max-h-screen object-contain"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
                target.nextElementSibling?.classList.remove("hidden");
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;