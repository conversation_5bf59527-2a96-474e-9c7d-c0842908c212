import { Search } from "lucide-react";

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  fullWidth?: boolean;
}

export default function SearchInput({
  value,
  onChange,
  placeholder = "Search",
  className = "",
  fullWidth = false,
}: SearchInputProps) {
  return (
    <div className={`relative inline-block ${fullWidth ? "w-full" : "w-fit"} ${className}`}>
      <div className="relative">
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className="w-full px-6 py-3 pr-10 rounded-full bg-white shadow-sm transition text-sm text-gray-700 placeholder:text-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500"
        />
        <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
      </div>
    </div>
  );
}