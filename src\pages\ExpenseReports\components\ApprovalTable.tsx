import { useNavigate } from 'react-router-dom';
import type { ApprovalData } from '../../../mockData/mockData';

interface ApprovalTableProps {
  data: ApprovalData[];
}

const ApprovalTable = ({ data }: ApprovalTableProps) => {
  const navigate = useNavigate();

  const handleNameClick = (employeeId: string) => {
    navigate(`/expense-reports/${employeeId}`);
  };

  return (
    <table className="w-full">
      <thead>
        <tr className="border-b border-gray-200">
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Employee Name</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Department</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Designation</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Expense Category</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Submission Date</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Total Amount</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Actions</th>
        </tr>
      </thead>
      <tbody>
        {data.length > 0 ? (
          data.map((approval) => (
            <tr key={approval.id} className="border-b border-gray-100 hover:bg-gray-50">
              <td className="py-4 px-4 text-sm text-gray-900">
                <button
                  onClick={() => handleNameClick(approval.id)}
                  className="text-teal-600 hover:text-teal-800 hover:underline cursor-pointer"
                >
                  {approval.employeeName}
                </button>
              </td>
              <td className="py-4 px-4 text-sm text-gray-600">{approval.department}</td>
              <td className="py-4 px-4 text-sm text-gray-600">{approval.designation}</td>
              <td className="py-4 px-4 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <span className="w-6 h-6 bg-gray-800 rounded flex items-center justify-center">
                    <img src="/categoryIcon.svg" alt="Category Icon" />
                  </span>
                  {approval.expenseCategory}
                </div>
              </td>
              <td className="py-4 px-4 text-sm text-gray-600">{approval.submissionDate}</td>
              <td className="py-4 px-4 text-sm text-gray-900 font-medium">
                ${approval.totalAmount.toFixed(2)}
              </td>
              <td className="py-4 px-4">
                <button className="text-sm text-gray-500 hover:text-gray-700">view</button>
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={7} className="py-8 px-4 text-center text-gray-500">
              No approvals found
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
};

export default ApprovalTable;
