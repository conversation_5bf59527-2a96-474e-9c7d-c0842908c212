import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../../components/Button';
import InputField from '../../../components/InputField';
import FormDropdown from '../../../components/FormDropdown';

const receiptOptions = ['Required', 'Optional', 'Not Required'];
const departmentOptions = ['All Departments', 'Finance', 'Marketing', 'Engineering', 'HR'];
const categoryOptions = ['All Categories', 'Travel', 'Meals', 'Accommodation', 'Office Supplies'];
const destinationOptions = ['Domestic', 'International', 'Both'];
const bookingClassOptions = ['Economy', 'Business', 'First Class'];
const advanceBookingOptions = ['Not Required', '1 Week', '2 Weeks', '1 Month'];
const roleOptions = ['All Roles', 'Manager', 'Employee', 'Admin', 'Finance Approver'];

const AddPolicy = () => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    policyName: '',
    description: '',
    maxFrequency: '',
    receiptRequirement: '',
    departments: '',
    categories: '',
    maxAmount: '',
    perDiem: '',
    destinationTypes: '',
    bookingClass: '',
    advanceBooking: '',
    roles: '',
  });

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCancel = () => {
    navigate('/policies');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log(formData);
    navigate('/policies');
    alert("Policy added successfully!");
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Policy Management</h1>
          <div className="flex items-center">
            <span
              className="cursor-pointer hover:text-teal-600 transition-colors text-sm text-gray-500 font-semibold"
              onClick={() => navigate('/')}
            >
              Dashboard
            </span>
            <span className="mx-2 text-sm text-gray-500">/</span>
            <span
              className="cursor-pointer hover:text-teal-600 transition-colors text-sm text-gray-500 font-semibold"
              onClick={() => navigate('/policies')}
            >
              Policy Management
            </span>
            <span className="mx-2 text-sm text-gray-500">/</span>
            <span className="text-sm text-teal-600 font-semibold">
              New Policy
            </span>
          </div>
        </div>
        <Button
          className="brand-gradient px-8 py-3 rounded-sm text-sm text-white"
          onClick={handleCancel}
        >
          Cancel Policy
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h2 className="text-lg font-semibold text-gray-600 mb-6">Policy Information</h2>

            <div className='space-y-3'>
              <InputField
                size="sm"
                label="Policy Name"
                value={formData.policyName}
                onChange={(val) => handleChange('policyName', val)}
                placeholder="Enter policy name"
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea
                  rows={5}
                  value={formData.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                  placeholder="Enter description"
                  className="w-full border border-gray-300 rounded-md text-sm text-gray-500 font-semibold bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 px-4 py-3"
                />
              </div>

              <InputField
                size="sm"
                label="Maximum Frequency"
                value={formData.maxFrequency}
                onChange={(val) => handleChange('maxFrequency', val)}
                placeholder="Enter frequency"
              />

              <FormDropdown
                size="sm"
                value={formData.receiptRequirement}
                onChange={(val) => handleChange('receiptRequirement', val)}
                options={receiptOptions}
                label="Receipt Requirement"
                placeholder="Select requirement"
              />
            </div>

            <h3 className="text-sm font-bold text-gray-500 my-8">Applicability (Optional)</h3>

            <div className='space-y-3'>
              <FormDropdown
                size="sm"
                value={formData.departments}
                onChange={(val) => handleChange('departments', val)}
                options={departmentOptions}
                label="Applicable Department"
                placeholder="Select department"
              />

              <FormDropdown
                size="sm"
                value={formData.roles}
                onChange={(val) => handleChange('roles', val)}
                options={roleOptions}
                label="Applicable Role"
                placeholder="Select role"
              />
            </div>
          </div>

          <div className='space-y-5'>
            <h2 className="text-lg font-semibold text-gray-600 mb-6">Policy Rules/Guidelines</h2>
            <FormDropdown
              size="sm"
              value={formData.categories}
              onChange={(val) => handleChange('categories', val)}
              options={categoryOptions}
              label="Categories"
              placeholder="Select categories"
            />

            <InputField
              size="sm"
              label="Maximum Amount"
              value={formData.maxAmount}
              onChange={(val) => handleChange('maxAmount', val)}
              placeholder="Enter amount"
            />

            <InputField
              size="sm"
              label="Enter Amount"
              value={formData.perDiem}
              onChange={(val) => handleChange('perDiem', val)}
              placeholder="Enter amount"
            />

            <FormDropdown
              size="sm"
              value={formData.destinationTypes}
              onChange={(val) => handleChange('destinationTypes', val)}
              options={destinationOptions}
              label="Destination Types(Travel)"
              placeholder="Select destination types"
            />

            <FormDropdown
              size="sm"
              value={formData.bookingClass}
              onChange={(val) => handleChange('bookingClass', val)}
              options={bookingClassOptions}
              label="Booking Class(Travel)"
              placeholder="Select booking class"
            />

            <FormDropdown
              size="sm"
              value={formData.advanceBooking}
              onChange={(val) => handleChange('advanceBooking', val)}
              options={advanceBookingOptions}
              label="Advance Booking Requirement(Travel)"
              placeholder="Select requirement"
            />

            <div className="mt-16 flex justify-center gap-5">
              <Button
                type="submit"
                className="px-16 py-3 rounded-full font-semibold border-2 border-teal-900 text-sm text-teal-900"
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="px-5 py-3 rounded-full brand-gradient text-sm text-white"
              >
                Confirm and Submit
              </Button>
            </div>

          </div>
        </div>
      </form>
    </div>
  );
};

export default AddPolicy;
